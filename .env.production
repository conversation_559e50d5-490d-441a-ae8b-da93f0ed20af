# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://bdgmmdbsuxexhqaukqzr.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkZ21tZGJzdXhleGhxYXVrcXpyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY2MzgwNTYsImV4cCI6MjA2MjIxNDA1Nn0.bbRqeukZrHyq04gyu5H-xE87n7IJfIhKI1zLMxUMflk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkZ21tZGJzdXhleGhxYXVrcXpyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjYzODA1NiwiZXhwIjoyMDYyMjE0MDU2fQ.O129M5t32UNuDCzvi18_Mnk_rNaKo3Ks-YIbG-qYVDg

# Database URL for Prisma (replace 'password' with the actual Supabase database password)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Application Configuration
NEXT_PUBLIC_SITE_URL=https://nougatapp.com

