# SQLite to Supabase Migration Guide

This document outlines the steps to migrate the Kanbany application from SQLite to Supabase using MCP tools.

## Prerequisites

1. A Supabase account and project
2. Access to MCP Supabase tools
3. Node.js and npm

## Configuration

Update the `.env.local` file with your Supabase credentials:

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database URL for Prisma
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Application Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## Database Migration

Use the MCP Supabase tools to apply migrations:

```
// List projects
mcp_supabase_list_projects

// Get project details
mcp_supabase_get_project --id <your-project-id>

// Apply migrations
mcp_supabase_apply_migration --project_id <your-project-id> --name init_schema --query <sql-content>
```

## Important Note on User IDs

Supabase uses UUID format for user IDs, while our previous SQLite implementation used string IDs. The schema has been updated to reference the UUID format correctly.

## Authentication

The application now uses Supabase's Magic Link authentication:

1. Users will sign in with their email
2. They will receive a magic link in their email
3. Clicking the link will authenticate them and redirect to the app

## User-Specific Data

With the migration to Supabase:

1. Each user now has their own private data (tasks, goals, visions)
2. Data is associated with user accounts via UUID references
3. Authentication is required to access the application

## Troubleshooting

If you encounter issues:

1. Check that your Supabase credentials are correct
2. Ensure the database tables have been properly created
3. Verify that the Prisma schema matches the Supabase database structure
4. Check for CORS issues if accessing from different domains 