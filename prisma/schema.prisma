// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Models for Kanban Board Application

model User {
  id         String      @id @db.Uuid
  email      String      @unique
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  visions    Vision[]
  goals      Goal[]
  tasks      Task[]
  brainDumps BrainDump[]
}

model Vision {
  id          String   @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  goals       Goal[]
  userId      String?  @db.Uuid
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
}

model Goal {
  id          String   @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  visionId    String?
  vision      Vision?  @relation(fields: [visionId], references: [id], onDelete: SetNull)
  tasks       Task[]
  userId      String?  @db.Uuid
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
}

model Task {
  id          String   @id @default(cuid())
  title       String
  description String?
  status      String   @default("monday") // "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"
  date        String?  // ISO format date (YYYY-MM-DD)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  goalId      String?
  goal        Goal?    @relation(fields: [goalId], references: [id], onDelete: SetNull)
  tags        Tag[]
  completed   Boolean  @default(false)
  userId      String?  @db.Uuid
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  tasks     Task[]
}

model BrainDump {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String   @db.Uuid
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
