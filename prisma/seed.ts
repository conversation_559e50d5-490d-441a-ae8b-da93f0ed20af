import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  await prisma.task.deleteMany({});
  await prisma.tag.deleteMany({});
  await prisma.goal.deleteMany({});
  await prisma.vision.deleteMany({});

  // Create visions
  const vision1 = await prisma.vision.create({
    data: {
      title: 'Healthy Lifestyle',
      description: 'Achieve a balanced and healthy lifestyle',
    },
  });

  const vision2 = await prisma.vision.create({
    data: {
      title: 'Career Growth',
      description: 'Advance in my professional career',
    },
  });

  const vision3 = await prisma.vision.create({
    data: {
      title: 'Personal Projects',
      description: 'Complete personal projects and hobbies',
    },
  });

  // Create goals
  const goal1 = await prisma.goal.create({
    data: {
      title: 'Regular Exercise',
      description: 'Exercise at least 3 times a week',
      visionId: vision1.id,
    },
  });

  const goal2 = await prisma.goal.create({
    data: {
      title: 'Balanced Diet',
      description: 'Eat a balanced diet with plenty of fruits and vegetables',
      visionId: vision1.id,
    },
  });

  const goal3 = await prisma.goal.create({
    data: {
      title: 'Learn New Skills',
      description: 'Acquire new skills relevant to my career',
      visionId: vision2.id,
    },
  });

  const goal4 = await prisma.goal.create({
    data: {
      title: 'Networking',
      description: 'Expand professional network',
      visionId: vision2.id,
    },
  });

  const goal5 = await prisma.goal.create({
    data: {
      title: 'Home Renovation',
      description: 'Complete home improvement projects',
      visionId: vision3.id,
    },
  });

  const goal6 = await prisma.goal.create({
    data: {
      title: 'Photography',
      description: 'Improve photography skills',
      visionId: vision3.id,
    },
  });

  // Create tags
  const tag1 = await prisma.tag.create({
    data: {
      name: 'Health',
    },
  });

  const tag2 = await prisma.tag.create({
    data: {
      name: 'Fitness',
    },
  });

  const tag3 = await prisma.tag.create({
    data: {
      name: 'Career',
    },
  });

  const tag4 = await prisma.tag.create({
    data: {
      name: 'Learning',
    },
  });

  const tag5 = await prisma.tag.create({
    data: {
      name: 'Home',
    },
  });

  const tag6 = await prisma.tag.create({
    data: {
      name: 'Creative',
    },
  });

  const tag7 = await prisma.tag.create({
    data: {
      name: 'Urgent',
    },
  });

  const tag8 = await prisma.tag.create({
    data: {
      name: 'Important',
    },
  });

  // Create tasks
  const tasks = [
    {
      title: 'Morning run in the park',
      description: 'Run for 30 minutes at the local park',
      status: 'monday',
      goalId: goal1.id,
      tags: [tag1.id, tag2.id],
    },
    {
      title: 'Strength training session',
      description: 'Complete a full-body strength workout',
      status: 'wednesday',
      goalId: goal1.id,
      tags: [tag2.id],
    },
    {
      title: 'Yoga class',
      description: 'Attend the evening yoga class at the gym',
      status: 'friday',
      goalId: goal1.id,
      tags: [tag1.id, tag2.id],
    },
    {
      title: 'Meal prep for the week',
      description: 'Prepare healthy meals for the entire week',
      status: 'sunday',
      goalId: goal2.id,
      tags: [tag1.id],
    },
    {
      title: 'Buy fresh produce',
      description: 'Visit the farmers market for fresh fruits and vegetables',
      status: 'saturday',
      goalId: goal2.id,
      tags: [tag1.id],
    },
    {
      title: 'Learn React hooks',
      description: 'Complete tutorial on React hooks and context API',
      status: 'tuesday',
      goalId: goal3.id,
      tags: [tag3.id, tag4.id],
    },
    {
      title: 'TypeScript advanced types',
      description: 'Study advanced TypeScript features and types',
      status: 'thursday',
      goalId: goal3.id,
      tags: [tag3.id, tag4.id],
    },
    {
      title: 'Attend industry conference',
      description: 'Attend the tech conference downtown',
      status: 'friday',
      goalId: goal4.id,
      tags: [tag3.id, tag8.id],
    },
    {
      title: 'Coffee with mentor',
      description: 'Meet with career mentor for advice',
      status: 'wednesday',
      goalId: goal4.id,
      tags: [tag3.id],
    },
    {
      title: 'Update LinkedIn profile',
      description: 'Update work experience and skills on LinkedIn',
      status: 'monday',
      goalId: goal4.id,
      tags: [tag3.id],
    },
    {
      title: 'Paint living room',
      description: 'Paint the living room walls',
      status: 'saturday',
      goalId: goal5.id,
      tags: [tag5.id],
    },
    {
      title: 'Fix leaking faucet',
      description: 'Repair the leaking faucet in the bathroom',
      status: 'sunday',
      goalId: goal5.id,
      tags: [tag5.id, tag7.id],
    },
    {
      title: 'Landscape design',
      description: 'Plan the new garden layout',
      status: 'tuesday',
      goalId: goal5.id,
      tags: [tag5.id, tag6.id],
    },
    {
      title: 'Practice portrait photography',
      description: 'Take portrait photos with different lighting conditions',
      status: 'saturday',
      goalId: goal6.id,
      tags: [tag6.id],
    },
    {
      title: 'Edit travel photos',
      description: 'Edit and organize photos from last trip',
      status: 'thursday',
      goalId: goal6.id,
      tags: [tag6.id],
    },
    {
      title: 'Photography course',
      description: 'Complete the online photography composition course',
      status: 'monday',
      goalId: goal6.id,
      tags: [tag4.id, tag6.id],
    },
    {
      title: 'Schedule dentist appointment',
      description: 'Call to schedule annual dental checkup',
      status: 'tuesday',
      goalId: goal1.id,
      tags: [tag1.id, tag7.id],
    },
    {
      title: 'Review quarterly goals',
      description: 'Assess progress on career quarterly goals',
      status: 'friday',
      goalId: goal3.id,
      tags: [tag3.id, tag8.id],
    },
    {
      title: 'Organize home office',
      description: 'Clean and organize the home office space',
      status: 'wednesday',
      goalId: goal5.id,
      tags: [tag5.id],
    },
    {
      title: 'Experiment with night photography',
      description: 'Practice long exposure night photography techniques',
      status: 'thursday',
      goalId: goal6.id,
      tags: [tag6.id],
    }
  ];

  // Add all tasks
  for (const task of tasks) {
    await prisma.task.create({
      data: {
        title: task.title,
        description: task.description,
        status: task.status,
        goalId: task.goalId,
        tags: {
          connect: task.tags.map(tagId => ({ id: tagId })),
        },
      },
    });
  }

  console.log('Database has been seeded with 20 tasks!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
