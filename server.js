// server.js
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');
const fs = require('fs');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

// Create the Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
    createServer(async (req, res) => {
        try {
            // Parse the URL
            const parsedUrl = parse(req.url, true);
            const { pathname } = parsedUrl;
            
            // Check if the request is for an uploaded file
            if (pathname.startsWith('/uploads/')) {
                console.log('[SERVER] Handling upload file request:', pathname);
                
                // Get the file path
                const filePath = path.join(__dirname, 'public', pathname);
                
                // Check if the file exists
                fs.stat(filePath, (err, stats) => {
                    if (err || !stats.isFile()) {
                        console.error('[SERVER] File not found:', filePath);
                        // If the file doesn't exist, let Next.js handle it
                        handle(req, res, parsedUrl);
                        return;
                    }
                    
                    // Determine content type from file extension
                    const ext = path.extname(filePath).toLowerCase();
                    let contentType = 'application/octet-stream';
                    
                    switch (ext) {
                        case '.jpg':
                        case '.jpeg':
                            contentType = 'image/jpeg';
                            break;
                        case '.png':
                            contentType = 'image/png';
                            break;
                        case '.gif':
                            contentType = 'image/gif';
                            break;
                        case '.webp':
                            contentType = 'image/webp';
                            break;
                    }
                    
                    // Read and serve the file
                    fs.readFile(filePath, (err, data) => {
                        if (err) {
                            console.error('[SERVER] Error reading file:', err);
                            res.statusCode = 500;
                            res.end('Internal Server Error');
                            return;
                        }
                        
                        res.setHeader('Content-Type', contentType);
                        res.end(data);
                        console.log('[SERVER] Successfully served file:', pathname);
                    });
                });
            } else {
                // Let Next.js handle all other requests
                await handle(req, res, parsedUrl);
            }
        } catch (err) {
            console.error('Error occurred handling', req.url, err);
            res.statusCode = 500;
            res.end('Internal Server Error');
        }
    }).listen(port, (err) => {
        if (err) throw err;
        console.log(`> Server listening on http://${hostname}:${port}`);
        console.log('> Using custom server for uploads handling');
    });
});