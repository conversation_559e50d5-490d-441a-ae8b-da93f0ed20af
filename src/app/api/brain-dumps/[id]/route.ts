import { NextRequest, NextResponse } from 'next/server';
import { deleteBrainDump } from '@/lib/supabase-data';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// Helper to get current user from server context
async function getCurrentUser() {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value;
          },
          set() {},
          remove() {},
        },
      }
    );
    
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  } catch (error) {
    console.error('Error getting current user in API route:', error);
    return null;
  }
}

// DELETE /api/brain-dumps/[id] - Delete a brain dump
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { id } = await context.params;
    
    if (!id) {
      return NextResponse.json({ error: 'Brain dump ID is required' }, { status: 400 });
    }
    
    await deleteBrainDump(id);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting brain dump:', error);
    return NextResponse.json({ error: 'Failed to delete brain dump' }, { status: 500 });
  }
} 