import { NextRequest, NextResponse } from 'next/server';
import { fetchBrainDumps, createBrainDump } from '@/lib/supabase-data';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// Helper to get current user from server context
async function getCurrentUser() {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value;
          },
          set() {},
          remove() {},
        },
      }
    );
    
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  } catch (error) {
    console.error('Error getting current user in API route:', error);
    return null;
  }
}

// GET /api/brain-dumps - Get all brain dumps for current user
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const brainDumps = await fetchBrainDumps();
    
    return NextResponse.json(brainDumps);
  } catch (error) {
    console.error('Error fetching brain dumps:', error);
    return NextResponse.json({ error: 'Failed to fetch brain dumps' }, { status: 500 });
  }
}

// POST /api/brain-dumps - Create a new brain dump
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await request.json();
    const { content } = body;
    
    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 });
    }
    
    // Create the brain dump
    const brainDump = await createBrainDump({ content: content.trim() });
    
    return NextResponse.json(brainDump, { status: 201 });
  } catch (error) {
    console.error('Error creating brain dump:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }
    return NextResponse.json({ error: 'Failed to create brain dump', details: error instanceof Error ? error.message : String(error) }, { status: 500 });
  }
} 