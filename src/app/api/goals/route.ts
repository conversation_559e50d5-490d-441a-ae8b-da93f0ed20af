import { NextRequest, NextResponse } from 'next/server';
import { fetchGoals, createGoal } from '@/lib/supabase-data';

// GET /api/goals - Get all goals
export async function GET() {
  try {
    const goals = await fetchGoals();
    
    return NextResponse.json(goals);
  } catch (error) {
    console.error('Error fetching goals:', error);
    return NextResponse.json({ error: 'Failed to fetch goals' }, { status: 500 });
  }
}

// POST /api/goals - Create a new goal
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description, visionId } = body;
    
    const goal = await createGoal({ title, description, visionId });
    
    return NextResponse.json(goal, { status: 201 });
  } catch (error) {
    console.error('Error creating goal:', error);
    return NextResponse.json({ error: 'Failed to create goal' }, { status: 500 });
  }
}
