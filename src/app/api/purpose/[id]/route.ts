import { NextResponse } from 'next/server';
import { updatePurpose, deletePurpose } from '@/lib/supabase-data';

// PUT /api/purpose/[id] - Update a purpose
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { mission } = body;

    if (!mission || typeof mission !== 'string' || mission.trim() === '') {
      return NextResponse.json({ error: 'Mission is required' }, { status: 400 });
    }

    const purpose = await updatePurpose(id, { mission: mission.trim() });
    return NextResponse.json(purpose);
  } catch (error) {
    console.error('Error updating purpose:', error);
    return NextResponse.json({ error: 'Failed to update purpose' }, { status: 500 });
  }
}

// DELETE /api/purpose/[id] - Delete a purpose
export async function DELETE(
  _request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    await deletePurpose(id);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting purpose:', error);
    return NextResponse.json({ error: 'Failed to delete purpose' }, { status: 500 });
  }
}
