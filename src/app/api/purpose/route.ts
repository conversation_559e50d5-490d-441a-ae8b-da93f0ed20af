import { NextResponse } from 'next/server';
import { fetchPurpose, createPurpose } from '@/lib/supabase-data';

// GET /api/purpose - Get the user's purpose
export async function GET() {
  try {
    const purpose = await fetchPurpose();
    return NextResponse.json(purpose);
  } catch (error) {
    console.error('Error fetching purpose:', error);
    return NextResponse.json({ error: 'Failed to fetch purpose' }, { status: 500 });
  }
}

// POST /api/purpose - Create a new purpose
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { mission } = body;

    if (!mission || typeof mission !== 'string' || mission.trim() === '') {
      return NextResponse.json({ error: 'Mission is required' }, { status: 400 });
    }

    const purpose = await createPurpose({ mission: mission.trim() });
    return NextResponse.json(purpose, { status: 201 });
  } catch (error) {
    console.error('Error creating purpose:', error);
    return NextResponse.json({ error: 'Failed to create purpose' }, { status: 500 });
  }
}
