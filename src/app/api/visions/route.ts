import { NextRequest, NextResponse } from 'next/server';
import { fetchVisions, createVision } from '@/lib/supabase-data';

// GET /api/visions - Get all visions
export async function GET() {
  try {
    const visions = await fetchVisions();
    return NextResponse.json(visions);
  } catch (error) {
    console.error('Error fetching visions:', error);
    return NextResponse.json({ error: 'Failed to fetch visions' }, { status: 500 });
  }
}

// POST /api/visions - Create a new vision
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description } = body;
    
    const vision = await createVision({ title, description });
    
    return NextResponse.json(vision, { status: 201 });
  } catch (error) {
    console.error('Error creating vision:', error);
    return NextResponse.json({ error: 'Failed to create vision' }, { status: 500 });
  }
}
