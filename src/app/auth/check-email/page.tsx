'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Mail } from 'lucide-react';

export default function CheckEmailPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 bg-[#0f141e] text-white">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <Link href="/" className="inline-block mb-6">
            <div className="flex items-center justify-center">
              <div className="relative w-10 h-10 mr-2">
                <svg 
                  viewBox="0 0 243.23 370.58" 
                  className="w-full h-full"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <linearGradient id="gradient-nav" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#00f9ff" />
                      <stop offset="100%" stopColor="#fc05ff" />
                    </linearGradient>
                  </defs>
                  <g>
                    <polygon fill="url(#gradient-nav)" points="121.61 278.08 243.23 185.59 121.84 92.79 .45 0 .33 98.96 113.45 185.43 .12 271.62 0 370.58 121.61 278.08"/>
                  </g>
                </svg>
              </div>
              <h1 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#00f9ff] to-[#fc05ff]">
                Nougat
              </h1>
            </div>
          </Link>
        </div>

        <div className="mt-8 bg-[#151b29] rounded-lg p-8 border border-[#00f9ff]/20 text-center">
          <div className="flex justify-center mb-6">
            <div className="h-16 w-16 rounded-full bg-[#00f9ff]/10 flex items-center justify-center">
              <Mail className="h-8 w-8 text-[#00f9ff]" />
            </div>
          </div>
          
          <h2 className="text-2xl font-semibold mb-4 text-white">Check your email</h2>
          
          <p className="text-gray-400 mb-4">
            We&apos;ve sent you a magic link to your email. Click on the link to sign in.
          </p>
          
          <p className="text-sm text-gray-500 mb-6">
            The link will expire after 24 hours.
          </p>

          <Link href="/auth/login" passHref>
            <Button variant="outline" className="mt-4 w-full border-[#00f9ff]/60 text-[#00f9ff] hover:bg-[#00f9ff]/10">
              Back to Login
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 