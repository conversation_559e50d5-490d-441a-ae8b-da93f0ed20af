'use client';

import { ThemeProvider } from "@/components/ui/theme-provider";
import { Inter } from "next/font/google";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className={`${inter.variable} font-sans antialiased min-h-screen scrollbar-hide`}>
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem={false}
        disableTransitionOnChange
      >
        <div className="relative flex min-h-screen flex-col scrollbar-hide">
          <main className="flex-1 scrollbar-hide">{children}</main>
        </div>
      </ThemeProvider>
    </div>
  );
} 