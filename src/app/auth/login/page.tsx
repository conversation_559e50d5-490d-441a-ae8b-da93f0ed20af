'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { signIn, isLoading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    try {
      await signIn(email);
    } catch (err) {
      setError('Failed to send login link. Please try again.');
      console.error(err);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 bg-[#0f141e] text-white">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <Link href="/" className="inline-block mb-6">
            <div className="flex items-center justify-center">
              <div className="relative w-10 h-10 mr-2">
                <svg 
                  viewBox="0 0 243.23 370.58" 
                  className="w-full h-full"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <linearGradient id="gradient-nav" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#00f9ff" />
                      <stop offset="100%" stopColor="#fc05ff" />
                    </linearGradient>
                  </defs>
                  <g>
                    <polygon fill="url(#gradient-nav)" points="121.61 278.08 243.23 185.59 121.84 92.79 .45 0 .33 98.96 113.45 185.43 .12 271.62 0 370.58 121.61 278.08"/>
                  </g>
                </svg>
              </div>
              <h1 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#00f9ff] to-[#fc05ff]">
                Nougat
              </h1>
            </div>
          </Link>
          <p className="mt-2 text-lg text-gray-400">
            Sign in or create an account
          </p>
        </div>

        <div className="mt-8 bg-[#151b29] rounded-lg p-8 border border-[#00f9ff]/20">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-white">
                Email address
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-[#0f141e]/80 border-[#00f9ff]/20 text-white"
                placeholder="<EMAIL>"
              />
            </div>

            {error && (
              <div className="text-sm text-[#fc05ff] font-medium">
                {error}
              </div>
            )}

            <Button
              type="submit"
              className="w-full bg-[#fc05ff] hover:bg-[#fc05ff]/90 text-white"
              disabled={isLoading}
            >
              {isLoading ? 'Sending...' : 'Send Magic Link'}
            </Button>

            <div className="text-center text-sm text-gray-400 mt-4">
              We&apos;ll email you a magic link for password-free access.
              <br />
              New users will automatically get an account.
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 