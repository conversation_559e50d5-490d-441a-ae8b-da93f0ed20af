@import "tailwindcss";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    
    --radius: 0.5rem;
  }
 
  .dark, [data-theme="dark"] {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    
    --card: 216 28% 17%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 75.1%;
    
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  /* Hide scrollbar for entire browser window */
  html {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
    overflow-y: auto;
  }
  
  html::-webkit-scrollbar {
    display: none;             /* Chrome, Safari and Opera */
  }
  
  body {
    background-color: hsl(222 47% 11%);
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
    overflow-y: auto;
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;             /* Chrome, Safari and Opera */
  }
}

/* Kanban Board Specific Styles */
@layer components {
  /* Kanban Columns */
  .kanban-column {
    /* background-color: hsl(216 28% 17%); */
    /* border-radius: 0.75rem 0.75rem 0 0; */
    padding: 0.5rem 0.5rem 0.5rem 0.5rem;
    /* border: 1px solid rgba(63, 73, 89, 0.3); */
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    overflow-y: auto;
    /* Remove scrollbar styling since we're using utility class */
    /* scrollbar-width: thin; */
    /* scrollbar-color: rgba(120, 135, 160, 0.3) transparent; */
    display: flex;
    flex-direction: column;
    position: relative;
  }
  
  /* Today's column styling - no longer used */
  /* .today-column {
    background-color: hsla(222, 47%, 20%, 1);
    border: 2px solid hsl(var(--primary)) !important;
    box-shadow: 0 0 15px rgba(var(--primary), 0.15);
  } */
  
  /* Custom scrollbar styling - hide these since we're now using scrollbar-hide */
  /* 
  .kanban-column::-webkit-scrollbar {
    width: 4px;
  }
  
  .kanban-column::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .kanban-column::-webkit-scrollbar-thumb {
    background-color: rgba(120, 135, 160, 0.3);
    border-radius: 10px;
  }
  */
  
  .kanban-column-header {
    position: sticky;
    top: 0;
    background-color: inherit;
    padding-bottom: 0.75rem;
    margin-bottom: 0.5rem;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .kanban-column-title {
    font-size: 0.95rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.02em;
  }
  
  .kanban-column-count {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
  }
  
  /* Task Cards */
  .task-card {
    background-color: hsl(214 30% 20%);
    color: hsl(var(--card-foreground));
    border: none !important;
    outline: none;
    border-radius: 0.5rem;
    overflow: visible;
    /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.15); */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
    margin-bottom: 0.75rem;
  }
  
  .task-card:hover {
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
  }
  
  .task-card-content {
    transition: all 0.2s ease;
  }
  
  .task-card-title {
    font-weight: 100;
    font-size: 15px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba(255, 255, 255, 0.95);
  }
  
  .task-card-description {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    line-height: 1.35;
  }
  
  .task-card-drageffect {
    opacity: 0.65;
    transform: scale(1.02);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }
  
  /* Empty Column State */
  .empty-column-state {
    border: 0px dashed rgba(120, 135, 160, 0.3);
    border-radius: 0.5rem;
    padding: 1.5rem 1rem;
    text-align: center;
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100px;
  }
  
  /* Badges */
  .badge-primary {
    background-color: rgba(0, 122, 255, 0.15);
    color: rgba(120, 170, 255, 0.9);
    border: none;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    letter-spacing: 0.02em;
    transition: all 0.15s ease;
  }
  
  .badge-secondary {
    background-color: rgba(78, 92, 110, 0.5);
    color: rgba(240, 240, 240, 0.85);
    border: none;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    letter-spacing: 0.02em;
    transition: all 0.15s ease;
  }
  
  .badge-outline {
    background-color: rgba(40, 50, 70, 0.4);
    color: rgba(240, 240, 240, 0.8);
    border: 1px solid rgba(78, 92, 110, 0.5);
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    letter-spacing: 0.02em;
    transition: all 0.15s ease;
  }
  
  /* Board Header */
  .kanban-board-header {
    background-color: hsl(222 47% 11%);
    padding: 1rem 0 0.5rem;
    margin-bottom: 1rem;
  }
  
  .kanban-board-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
  }
  
  /* Card Tags */
  .task-tag {
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    margin-bottom: 0.5rem;
    display: inline-block;
  }
  
  .task-tag-design {
    background-color: rgba(117, 74, 221, 0.2);
    color: rgba(157, 118, 255, 0.9);
  }
  
  .task-tag-marketing {
    background-color: rgba(74, 165, 221, 0.2);
    color: rgba(118, 191, 255, 0.9);
  }
  
  .task-tag-research {
    background-color: rgba(74, 221, 165, 0.2);
    color: rgba(118, 255, 191, 0.9);
  }
  
  /* Add Task Button */
  .add-task-btn {
    transition: all 0.2s ease;
    background-color: rgba(0, 122, 255, 0.9);
    border: none;
    font-weight: 500;
  }
  
  .add-task-btn:hover {
    transform: translateY(-1px);
    background-color: rgba(0, 122, 255, 1);
    box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
  }
  
  /* Task Card Footer */
  .task-card-footer {
    background-color: rgba(20, 30, 45, 0.4);
    padding: 0.5rem 1rem;
    border-top: 1px solid rgba(63, 73, 89, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  /* Colored Icons */
  .icon-edit {
    color: #4ade80;
  }
  
  .icon-delete {
    color: #f87171;
  }
  
  .icon-goal {
    color: #60a5fa;
  }
  
  .icon-calendar {
    color: #fcd34d;
  }
  
  .icon-grip {
    color: #a1a1aa;
  }
  
  .icon-target {
    color: #a78bfa;
  }
  
  .icon-description {
    color: #94a3b8;
  }
  
  /* Dropdown Menu Styling */
  .task-card .dropdown-menu-content {
    background-color: hsl(214 30% 20%);
    border: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.15);
  }
  
  /* Tooltip Styling */
  .task-card .tooltip-container {
    position: relative;
    z-index: 50;
    cursor: pointer;
  }
  
  .task-card .tooltip-content {
    z-index: 100;
    position: relative;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.4));
    min-width: 8rem;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background-color: hsl(214 30% 20%);
    border: 1px solid rgba(63, 73, 89, 0.2);
  }
  
  /* Task Dialog/Modal Styling */
  .task-dialog {
    background-color: hsl(216 28% 17%);
    border: 1px solid rgba(63, 73, 89, 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
  }
  
  .task-dialog .dialog-header {
    padding-bottom: 1rem;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid rgba(63, 73, 89, 0.2);
  }
  
  .task-dialog .dialog-title {
    font-size: 1.125rem;
    font-weight: 600;
    letter-spacing: -0.01em;
    color: rgba(255, 255, 255, 0.95);
  }
  
  .task-dialog .dialog-content {
    padding: 0.75rem 0;
  }
  
  .task-dialog .dialog-footer {
    padding-top: 1rem;
    margin-top: 0.5rem;
    border-top: 1px solid rgba(63, 73, 89, 0.2);
  }
  
  .task-dialog .form-label {
    font-weight: 500;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.375rem;
    display: block;
  }
  
  .task-dialog .form-field {
    margin-bottom: 1rem;
  }
  
  .task-dialog select {
    background-color: hsl(214 30% 20%);
    border-color: rgba(63, 73, 89, 0.3);
    color: rgba(255, 255, 255, 0.9);
  }

  .task-dialog select option {
    background-color: hsl(214 30% 20%);
    color: rgba(255, 255, 255, 0.9);
  }
  
  .task-dialog-input {
    background-color: hsl(214 30% 20%);
    border-color: rgba(63, 73, 89, 0.3);
    color: rgba(255, 255, 255, 0.9);
  }
  
  .task-dialog-input:focus {
    border-color: rgba(100, 150, 230, 0.5);
    box-shadow: 0 0 0 2px rgba(100, 150, 230, 0.2);
  }
  
  /* Task Cards Container */
  .task-cards-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(120, 135, 160, 0.3) transparent;
  }
  
  .task-cards-container::-webkit-scrollbar {
    width: 4px;
  }
  
  .task-cards-container::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .task-cards-container::-webkit-scrollbar-thumb {
    background-color: rgba(120, 135, 160, 0.3);
    border-radius: 10px;
  }
  
  /* Profile Avatars */
  .profile-avatar {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    border: 2px solid hsl(214 30% 20%);
    overflow: hidden;
  }
  
  .avatar-group {
    display: flex;
  }
  
  .avatar-group .profile-avatar {
    margin-left: -0.5rem;
  }
  
  .avatar-group .profile-avatar:first-child {
    margin-left: 0;
  }
  
  /* Status Indicators */
  .status-badge {
    height: 0.5rem;
    width: 0.5rem;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.375rem;
  }
  
  .status-in-progress {
    background-color: #4a9eff;
  }
  
  .status-done {
    background-color: #4aff9e;
  }
  
  .status-impeded {
    background-color: #ff4a4a;
  }
}
