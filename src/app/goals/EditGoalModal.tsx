'use client';

import { useState, useEffect, useCallback } from 'react';
import { useKanbanStore, Goal } from '@/lib/store';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/lib/auth';

interface EditGoalModalProps {
  isOpen: boolean;
  onClose: () => void;
  goal: Goal;
}

export default function EditGoalModal({ isOpen, onClose, goal }: EditGoalModalProps) {
  const { updateGoal, visions, setVisions } = useKanbanStore();
  const [title, setTitle] = useState(goal.title);
  const [description, setDescription] = useState(goal.description || '');
  const [selectedVisionId, setSelectedVisionId] = useState<string | undefined>(goal.visionId);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const fetchVisions = useCallback(async () => {
    try {
      const response = await fetch('/api/visions');
      if (response.ok) {
        const data = await response.json();
        setVisions(data);
      } else {
        console.error('Failed to fetch visions');
      }
    } catch (error) {
      console.error('Error fetching visions:', error);
    }
  }, [setVisions]);

  // Fetch visions when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchVisions();
    }
  }, [isOpen, fetchVisions]);

  // Update form when goal changes
  useEffect(() => {
    setTitle(goal.title);
    setDescription(goal.description || '');
    setSelectedVisionId(goal.visionId);
  }, [goal]);



  const handleSubmit = async () => {
    if (!title.trim()) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/goals/${goal.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          visionId: selectedVisionId,
          userId: user?.id
        }),
      });
      
      if (response.ok) {
        const updatedGoal = await response.json();
        updateGoal(goal.id, updatedGoal);
        onClose();
      } else {
        console.error('Failed to update goal');
      }
    } catch (error) {
      console.error('Error updating goal:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title">Edit Goal</DialogTitle>
        </DialogHeader>
        <div className="dialog-content space-y-4">
          <div className="form-field">
            <label htmlFor="title" className="form-label">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Goal title"
              className="task-dialog-input"
            />
          </div>
          <div className="form-field">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Goal description"
              className="task-dialog-input min-h-[100px]"
            />
          </div>
          <div className="form-field">
            <label htmlFor="vision" className="form-label">
              Vision
            </label>
            <select
              id="vision"
              value={selectedVisionId || ''}
              onChange={(e) => setSelectedVisionId(e.target.value || undefined)}
              className="flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a vision</option>
              {visions.map((vision) => (
                <option key={vision.id} value={vision.id}>
                  {vision.title}
                </option>
              ))}
            </select>
          </div>
        </div>
        <DialogFooter className="dialog-footer">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
