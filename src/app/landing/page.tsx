'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircle2, CheckCircle, ArrowRight, Kanban, Target, Telescope } from 'lucide-react';

// Feature card component with clean style
function FeatureCard({ title, description, icon }: { title: string; description: string; icon: React.ReactNode }) {
  return (
    <div className="bg-[#151b29] border border-[#00f9ff]/40 rounded-lg p-8 transition-all hover:translate-y-[-2px]">
      <div className="text-4xl mb-4 bg-[#0f141e] w-16 h-16 flex items-center justify-center rounded-lg text-[#00f9ff]">{icon}</div>
      <h3 className="text-xl font-semibold mb-3 text-[#fc05ff]">{title}</h3>
      <p className="text-gray-400 leading-relaxed">{description}</p>
    </div>
  );
}

export default function LandingPage() {
  const { user } = useAuth();
  const router = useRouter();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [router, user]);

  // If user is authenticated, don't render the landing page
  if (user) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col bg-[#0f141e] relative text-white">
      {/* Content with z-index to appear above background */}
      <div className="relative z-10">
        {/* Header */}
        <header className="border-b border-[#00f9ff]/20 py-4 px-6 bg-[#0f141e]">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <div className="flex items-center">
              <div className="relative w-8 h-8 mr-2">
                <svg 
                  viewBox="0 0 243.23 370.58" 
                  className="w-full h-full"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <linearGradient id="gradient-nav" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#00f9ff" />
                      <stop offset="100%" stopColor="#fc05ff" />
                    </linearGradient>
                  </defs>
                  <g>
                    <polygon fill="url(#gradient-nav)" points="121.61 278.08 243.23 185.59 121.84 92.79 .45 0 .33 98.96 113.45 185.43 .12 271.62 0 370.58 121.61 278.08"/>
                  </g>
                </svg>
              </div>
              <span className="font-bold text-lg text-white">Nougat</span>
            </div>
            <div className="flex gap-4">
              <Link href="/auth/login">
                <Button variant="ghost" className="text-[#00f9ff] hover:text-[#00f9ff] hover:bg-[#00f9ff]/10">Sign In</Button>
              </Link>
              <Link href="/auth/login">
                <Button className="bg-[#fc05ff] text-white hover:bg-[#fc05ff]/90">Get Started</Button>
              </Link>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <div className="relative px-4 py-20 md:py-32 overflow-hidden">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 gap-12 items-center">
              <div className="text-center mx-auto">
                <span className="inline-block px-4 py-2 rounded-full border border-[#00f9ff]/30 bg-[#00f9ff]/5 text-[#00f9ff] text-sm font-medium mb-6">
                  Personal Productivity System
                </span>
                
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-transparent bg-clip-text bg-gradient-to-r from-[#00f9ff] to-[#fc05ff] mb-6">
                  Organize your life, achieve your goals
                </h1>
                
                <p className="text-xl text-gray-400 max-w-lg mb-10 leading-relaxed mx-auto">
                  Nougat helps you take control of your daily tasks, track progress on your personal goals, and turn your dreams into reality.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/auth/login">
                    <Button size="lg" className="bg-[#fc05ff] hover:bg-[#fc05ff]/90 text-white px-8 py-6 w-full sm:w-auto">
                      Get Started — Free Forever
                    </Button>
                  </Link>
                  
                  <Link href="/auth/login">
                    <Button size="lg" variant="outline" className="border-[#00f9ff]/60 text-[#00f9ff] hover:bg-[#00f9ff]/10 px-8 py-6 w-full sm:w-auto">
                      Sign In
                    </Button>
                  </Link>
                </div>
                
                <p className="mt-6 text-gray-400 flex items-center gap-2 justify-center">
                  <CheckCircle className="h-4 w-4 text-[#00f9ff]" />
                  <span>No credit card required</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Features Section */}
        <div className="py-20 px-4 bg-[#0f141e]/90 border-b border-gray-800">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 rounded-full border border-[#fc05ff]/30 bg-[#fc05ff]/5 text-[#fc05ff] text-sm font-medium mb-4">
                Key Features
              </span>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-[#00f9ff] to-[#fc05ff]">Tools for personal success</h2>
              <p className="text-xl text-gray-400 max-w-2xl mx-auto">
                Everything you need to organize your life, focus on what matters, and achieve your personal goals.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
              <FeatureCard 
                title="Personal Kanban"
                description="Visualize your workflow with flexible boards that adapt to your unique lifestyle and work patterns."
                icon={<Kanban size={32} />}
              />
              <FeatureCard 
                title="Goal Setting"
                description="Define measurable goals and track your progress with motivating visual indicators."
                icon={<Target size={32} />}
              />
              <FeatureCard 
                title="Vision Board"
                description="Create a visual representation of your dreams and break them down into actionable steps."
                icon={<Telescope size={32} />}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Free Version Highlight */}
      <div className="py-20 px-4 bg-[#0f141e] border-y border-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <span className="inline-block px-4 py-2 rounded-full border border-[#fc05ff]/30 bg-[#fc05ff]/5 text-[#fc05ff] text-sm font-medium mb-4">
                Free Forever
              </span>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-[#00f9ff] to-[#fc05ff]">No limits, no paywalls</h2>
              <p className="text-xl text-gray-400 mb-8 leading-relaxed">
                All features are included for free. Your personal growth and productivity shouldn&apos;t have limits.
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                {[
                  'Unlimited Boards',
                  'Unlimited Tasks',
                  'Goal Setting',
                  'Vision Board'
                ].map((feature) => (
                  <div key={feature} className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-[#00f9ff]" />
                    <span className="text-gray-400">{feature}</span>
                  </div>
                ))}
              </div>
              
              <Link href="/auth/login">
                <Button size="lg" className="bg-[#fc05ff] hover:bg-[#fc05ff]/90 text-white px-8 py-6">
                  Get Started Now <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
            
            <div className="bg-[#151b29] rounded-xl border border-[#00f9ff]/20 p-8">
              <h3 className="text-2xl font-bold mb-6 text-[#00f9ff]">Frequently Asked Questions</h3>
              
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-semibold mb-2 text-[#fc05ff]">Is Nougat really free?</h4>
                  <p className="text-gray-400">Yes! Nougat is completely free with no hidden costs or limitations. We believe everyone deserves access to powerful productivity tools.</p>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-2 text-[#fc05ff]">How is Nougat different from other apps?</h4>
                  <p className="text-gray-400">Nougat uniquely combines daily task management with long-term goal visualization, helping you connect everyday actions to your dreams.</p>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-2 text-[#fc05ff]">Will Nougat work for my specific needs?</h4>
                  <p className="text-gray-400">Absolutely! Nougat is designed to be highly customizable for any personal productivity system, whether you follow GTD, bullet journaling, or your own method.</p>
                </div>
                
                <div>
                  <h4 className="text-lg font-semibold mb-2 text-[#fc05ff]">How secure is my data?</h4>
                  <p className="text-gray-400">Your privacy matters. All your personal data is encrypted and stored securely, and we never share your information with third parties.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="py-20 px-4 bg-[#0f141e] relative overflow-hidden">
          <div className="max-w-4xl mx-auto text-center relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-[#00f9ff] to-[#fc05ff]">Ready to transform your personal productivity?</h2>
            <p className="text-xl text-gray-400 mb-10 max-w-2xl mx-auto">
              Join thousands of individuals who use Nougat to organize their lives and achieve their personal goals.
            </p>
            
            <Link href="/auth/login">
              <Button size="lg" className="bg-[#fc05ff] hover:bg-[#fc05ff]/90 text-white px-10 py-7 text-lg">
                Get Started — Free Forever
              </Button>
            </Link>
            
            <p className="mt-6 text-gray-500">
              No credit card required. All features included.
            </p>
          </div>
        </div>

        {/* Footer */}
        <footer className="py-16 px-4 bg-[#0f141e] text-white border-t border-gray-800">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-16 mb-16">
              <div>
                <div className="flex items-center mb-6">
                  <div className="relative w-8 h-8 mr-2">
                    <svg 
                      viewBox="0 0 243.23 370.58" 
                      className="w-full h-full text-[#00f9ff]"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <defs>
                        <linearGradient id="gradient-small-1" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#00f9ff" />
                          <stop offset="100%" stopColor="#fc05ff" />
                        </linearGradient>
                      </defs>
                      <g>
                        <polygon fill="url(#gradient-small-1)" points="121.61 278.08 243.23 185.59 121.84 92.79 .45 0 .33 98.96 113.45 185.43 .12 271.62 0 370.58 121.61 278.08"/>
                      </g>
                    </svg>
                  </div>
                  <span className="font-bold text-xl text-white">Nougat</span>
                </div>
                <p className="text-gray-400 mb-4 text-lg">
                  Organize your tasks, achieve your goals, and realize your personal vision.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-6 text-[#00f9ff] text-xl">Legal</h3>
                <ul className="space-y-4 text-gray-400">
                  <li><Link href="#" className="hover:text-white transition-colors">Privacy Policy</Link></li>
                  <li><Link href="#" className="hover:text-white transition-colors">Terms of Service</Link></li>
                  <li><Link href="#" className="hover:text-white transition-colors">Cookie Policy</Link></li>
                </ul>
              </div>
            </div>
            
            <div className="pt-8 border-t border-gray-800 flex justify-center items-center">
              <div className="text-gray-500 text-sm">
                © {new Date().getFullYear()} Nougat. All rights reserved.
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
} 