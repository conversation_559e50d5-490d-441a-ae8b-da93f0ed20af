'use client';

import { useState } from 'react';
import { useKanbanStore, Purpose } from '@/lib/store';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface EditPurposeModalProps {
  isOpen: boolean;
  onClose: () => void;
  purpose: Purpose | null;
}

export default function EditPurposeModal({ isOpen, onClose, purpose }: EditPurposeModalProps) {
  const { setPurpose } = useKanbanStore();
  const [mission, setMission] = useState(purpose?.mission || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!mission.trim()) {
      setError('Mission is required');
      return;
    }

    setIsSubmitting(true);

    try {
      if (purpose) {
        // Update existing purpose
        const response = await fetch(`/api/purpose/${purpose.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ mission }),
        });

        if (response.ok) {
          const updatedPurpose = await response.json();
          setPurpose(updatedPurpose);
          onClose();
        } else {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to update purpose');
        }
      } else {
        // Create new purpose
        const response = await fetch('/api/purpose', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ mission }),
        });

        if (response.ok) {
          const newPurpose = await response.json();
          setPurpose(newPurpose);
          onClose();
        } else {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to create purpose');
        }
      }
    } catch (error) {
      console.error('Error saving purpose:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[525px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title text-pink-400">{purpose ? 'Edit' : 'Define'} Your Purpose</DialogTitle>
          <DialogDescription>
            Define your mission statement that will guide your visions and goals.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="dialog-content space-y-4">
            <div className="form-field">
              <Label htmlFor="mission" className="form-label text-pink-400">
                Mission
              </Label>
              <Textarea
                id="mission"
                value={mission}
                onChange={(e) => setMission(e.target.value)}
                placeholder="Enter your mission statement..."
                className="task-dialog-input min-h-[150px]"
              />
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>
          </div>

          <DialogFooter className="dialog-footer">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-pink-600 hover:bg-pink-700"
            >
              {isSubmitting ? 'Saving...' : purpose ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
