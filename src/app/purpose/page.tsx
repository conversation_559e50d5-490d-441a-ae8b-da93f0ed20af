'use client';

import { useState, useEffect } from 'react';
import { useKanbanStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Edit, Pencil } from 'lucide-react';
import { Navbar } from "@/components/ui/navbar";
import EditPurposeModal from './EditPurposeModal';

export default function PurposePage() {
  const { purpose, setPurpose } = useKanbanStore();
  const [isEditPurposeModalOpen, setIsEditPurposeModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch purpose from the API
  useEffect(() => {
    const fetchPurpose = async () => {
      try {
        const response = await fetch('/api/purpose');
        if (response.ok) {
          const data = await response.json();
          setPurpose(data);
        } else {
          console.error('Failed to fetch purpose');
        }
      } catch (error) {
        console.error('Error fetching purpose:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPurpose();
  }, [setPurpose]);

  if (isLoading) {
    return (
      <div className="relative flex min-h-screen flex-col scrollbar-hide">
        <Navbar />
        <div className="flex justify-center items-center h-screen">Loading...</div>
      </div>
    );
  }

  return (
    <div className="relative flex min-h-screen flex-col scrollbar-hide mt-16">
      <Navbar />
      <div className="h-screen flex flex-col overflow-y-auto scrollbar-hide bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 w-full">
          <div className="flex justify-between items-center mb-6 bg-pink-600/10 p-4 rounded-lg shadow-inner">
            <div>
              <h1 className="text-2xl font-bold text-pink-400">Purpose</h1>
              <p className="text-gray-400">Define your mission and purpose</p>
            </div>
          </div>
        </div>
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex-1">
          {!purpose ? (
            <div className="flex flex-col items-center justify-center py-16 px-8 bg-gradient-to-br from-pink-600/8 via-pink-600/5 to-pink-600/8 rounded-xl shadow-inner border border-pink-500/10 backdrop-blur-sm">
              {/* Icon with subtle animation */}
              <div className="mb-6 p-4 bg-pink-600/10 rounded-full border border-pink-500/20 shadow-lg">
                <Pencil className="h-8 w-8 text-pink-400/80" />
              </div>

              {/* Enhanced typography with better hierarchy */}
              <h2 className="text-2xl font-bold text-pink-400 mb-3 tracking-tight">
                No purpose defined yet
              </h2>
              <p className="text-gray-300 text-center max-w-md leading-relaxed mb-8">
                Define your mission and purpose to guide your visions and goals.
              </p>

              {/* Enhanced call-to-action button */}
              <Button
                className="bg-gradient-to-r from-pink-600 to-pink-600 hover:from-pink-700 hover:to-pink-700 text-white font-medium px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5"
                onClick={() => setIsEditPurposeModalOpen(true)}
              >
                <Pencil className="mr-2 h-5 w-5" />
                Define Your Purpose
              </Button>
            </div>
          ) : (
            <div className="max-w-3xl mx-auto">
              <Card className="border border-pink-500/20 bg-pink-600/5 hover:bg-pink-600/10 transition-colors duration-200 shadow-md">
                <CardHeader className="pb-2">
                  <CardTitle className="text-pink-400 text-2xl">My Mission</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-200 text-lg leading-relaxed py-4">{purpose.mission}</p>
                </CardContent>
                <CardFooter className="flex justify-end space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditPurposeModalOpen(true)}
                    className="text-pink-400 hover:text-pink-300 hover:bg-pink-600/20"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}
        </main>

        <EditPurposeModal
          isOpen={isEditPurposeModalOpen}
          onClose={() => setIsEditPurposeModalOpen(false)}
          purpose={purpose}
        />
      </div>
    </div>
  );
}
