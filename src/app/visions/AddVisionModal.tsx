'use client';

import { useState } from 'react';
import { useKanbanStore } from '@/lib/store';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface AddVisionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AddVisionModal({ isOpen, onClose }: AddVisionModalProps) {
  const { addVision } = useKanbanStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!title.trim()) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/visions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
        }),
      });
      
      if (response.ok) {
        const newVision = await response.json();
        addVision(newVision);
        resetForm();
        onClose();
      } else {
        console.error('Failed to create vision');
      }
    } catch (error) {
      console.error('Error creating vision:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title">Add New Vision</DialogTitle>
        </DialogHeader>
        <div className="dialog-content space-y-4">
          <div className="form-field">
            <label htmlFor="title" className="form-label">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Vision title"
              className="task-dialog-input"
            />
          </div>
          <div className="form-field">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Vision description"
              className="task-dialog-input min-h-[100px]"
            />
          </div>
        </div>
        <DialogFooter className="dialog-footer">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Create Vision'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
