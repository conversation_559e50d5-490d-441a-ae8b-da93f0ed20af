'use client';

import { useState, useEffect } from 'react';
import { useKanbanStore, Vision } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { PlusCircle, Edit, Trash, Eye } from 'lucide-react';
import AddVisionModal from './AddVisionModal';
import EditVisionModal from './EditVisionModal';
import { Navbar } from "@/components/ui/navbar";

export default function VisionsPage() {
  const { visions, setVisions, deleteVision } = useKanbanStore();
  const [isAddVisionModalOpen, setIsAddVisionModalOpen] = useState(false);
  const [selectedVision, setSelectedVision] = useState<Vision | null>(null);
  const [isEditVisionModalOpen, setIsEditVisionModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch visions from the API
  useEffect(() => {
    const fetchVisions = async () => {
      try {
        const response = await fetch('/api/visions');
        if (response.ok) {
          const data = await response.json();
          setVisions(data);
        } else {
          console.error('Failed to fetch visions');
        }
      } catch (error) {
        console.error('Error fetching visions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVisions();
  }, [setVisions]);

  const handleDeleteVision = async (visionId: string) => {
    if (confirm('Are you sure you want to delete this vision?')) {
      try {
        const response = await fetch(`/api/visions/${visionId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          // Update the UI by removing the vision from the store
          deleteVision(visionId);
        } else {
          console.error('Failed to delete vision');
        }
      } catch (error) {
        console.error('Error deleting vision:', error);
      }
    }
  };

  const handleEditVision = (vision: Vision) => {
    setSelectedVision(vision);
    setIsEditVisionModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="relative flex min-h-screen flex-col scrollbar-hide">
        <Navbar />
        <div className="flex justify-center items-center h-screen">Loading...</div>
      </div>
    );
  }

  return (
    <div className="relative flex min-h-screen flex-col scrollbar-hide mt-16">
      <Navbar />
      <div className="h-screen flex flex-col overflow-y-auto scrollbar-hide bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 w-full">
          <div className="flex justify-between items-center mb-6 bg-purple-600/10 p-4 rounded-lg shadow-inner">
            <div>
              <h1 className="text-2xl font-bold text-violet-400">Visions</h1>
              <p className="text-gray-400">Manage your high-level visions</p>
            </div>
            <Button 
              onClick={() => setIsAddVisionModalOpen(true)}
              className="h-9 px-4 rounded-full cursor-pointer bg-purple-600/80 hover:bg-purple-600 transition-colors duration-200"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Vision
            </Button>
          </div>
        </div>
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex-1">
          {visions.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-8 bg-gradient-to-br from-purple-600/8 via-purple-600/5 to-violet-600/8 rounded-xl shadow-inner border border-purple-500/10 backdrop-blur-sm">
              {/* Icon with subtle animation */}
              <div className="mb-6 p-4 bg-purple-600/10 rounded-full border border-purple-500/20 shadow-lg">
                <Eye className="h-8 w-8 text-violet-400/80" />
              </div>

              {/* Enhanced typography with better hierarchy */}
              <h2 className="text-2xl font-bold text-violet-400 mb-3 tracking-tight">
                No visions yet
              </h2>
              <p className="text-gray-300 text-center max-w-md leading-relaxed mb-8">
                Start your journey by creating your first vision. Define your long-term goals and aspirations to guide your planning.
              </p>

              {/* Enhanced call-to-action button */}
              <Button
                className="bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 text-white font-medium px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5"
                onClick={() => setIsAddVisionModalOpen(true)}
              >
                <PlusCircle className="mr-2 h-5 w-5" />
                Create Your First Vision
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {visions.map((vision) => (
                <Card key={vision.id} className="border border-purple-500/20 bg-purple-600/5 hover:bg-purple-600/10 transition-colors duration-200 shadow-md">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-violet-400">{vision.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400">{vision.description}</p>
                    <div className="mt-4 p-2 bg-purple-600/10 rounded-lg">
                      <p className="text-sm font-medium text-violet-400">Goals:</p>
                      <p className="text-gray-300">
                        {vision.goals ? vision.goals.length : 0} goals linked to this vision
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditVision(vision)}
                      className="text-violet-400 hover:text-violet-300 hover:bg-purple-600/20"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteVision(vision.id)}
                      className="text-violet-400 hover:text-violet-300 hover:bg-purple-600/20"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </main>

        <AddVisionModal
          isOpen={isAddVisionModalOpen}
          onClose={() => setIsAddVisionModalOpen(false)}
        />

        {selectedVision && (
          <EditVisionModal
            isOpen={isEditVisionModalOpen}
            onClose={() => setIsEditVisionModalOpen(false)}
            vision={selectedVision}
          />
        )}
      </div>
    </div>
  );
}
