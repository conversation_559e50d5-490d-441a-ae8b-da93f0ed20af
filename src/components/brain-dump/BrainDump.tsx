'use client';

import { useState, useEffect, createContext, useContext } from 'react';
import { PlusCircle } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  She<PERSON><PERSON>itle,
} from '@/components/ui/sheet';

interface BrainDumpItem {
  id: string;
  content: string;
  createdAt: string;
}

// Create a context to manage the sheet's open state across components
interface BrainDumpContextType {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const BrainDumpContext = createContext<BrainDumpContextType | null>(null);

// Provider component that wraps the app
export function BrainDumpProvider({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <BrainDumpContext.Provider value={{ isOpen, setIsOpen }}>
      {children}
      <BrainDumpSheet />
    </BrainDumpContext.Provider>
  );
}

// Custom hook to use the Brain Dump context
export function useBrainDump() {
  const context = useContext(BrainDumpContext);
  if (!context) {
    throw new Error('useBrainDump must be used within a BrainDumpProvider');
  }
  return context;
}

// Button component that goes in the navbar
export function BrainDumpButton() {
  const { setIsOpen } = useBrainDump();
  
  return (
    <Button 
      variant="outline" 
      size="sm" 
      className="bg-gray-800 border-gray-700 hover:bg-gray-700 flex items-center cursor-pointer"
      onClick={() => setIsOpen(true)}
    >
      <Image
        src="/assets/icons/brain.png"
        alt="Brain Dump"
        width={20}
        height={20}
        className="mr-2"
      />
      Brain Dump
    </Button>
  );
}

// Sheet component with all the brain dump functionality
function BrainDumpSheet() {
  const { isOpen, setIsOpen } = useBrainDump();
  const [brainDumpItems, setBrainDumpItems] = useState<BrainDumpItem[]>([]);
  const [newDumpText, setNewDumpText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingItem, setIsAddingItem] = useState(false);

  // Load brain dump items from Supabase when sheet opens
  useEffect(() => {
    if (isOpen) {
      fetchBrainDumpItems();
    }
  }, [isOpen]);

  const fetchBrainDumpItems = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/brain-dumps');
      if (response.ok) {
        const items = await response.json();
        setBrainDumpItems(items);
      } else {
        console.error('Failed to fetch brain dump items');
      }
    } catch (error) {
      console.error('Error fetching brain dump items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new brain dump item
  const addBrainDumpItem = async () => {
    if (newDumpText.trim() === '' || isAddingItem) return;
    
    try {
      setIsAddingItem(true);
      const response = await fetch('/api/brain-dumps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: newDumpText.trim() }),
      });

      if (response.ok) {
        const newItem = await response.json();
        setBrainDumpItems([newItem, ...brainDumpItems]);
        setNewDumpText('');
      } else {
        console.error('Failed to create brain dump item');
      }
    } catch (error) {
      console.error('Error creating brain dump item:', error);
    } finally {
      setIsAddingItem(false);
    }
  };

  // Remove a brain dump item
  const removeBrainDumpItem = async (id: string) => {
    try {
      const response = await fetch(`/api/brain-dumps/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setBrainDumpItems(brainDumpItems.filter(item => item.id !== id));
      } else {
        console.error('Failed to delete brain dump item');
      }
    } catch (error) {
      console.error('Error deleting brain dump item:', error);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetContent 
        side="right" 
        className="w-[400px] sm:w-[540px] overflow-y-auto border-l border-gray-800 shadow-2xl"
      >
        <SheetHeader className="mb-2">
          <SheetTitle className="flex items-center text-white">
            <Image
              src="/assets/icons/brain.png"
              alt="Brain Dump"
              width={20}
              height={20}
              className="mr-2"
            />
            Brain Dump
          </SheetTitle>
        </SheetHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div className="flex flex-col gap-2">
              <Textarea
                placeholder="Dump your thoughts here..."
                className="min-h-[100px] resize-none bg-[hsl(214_30%_20%)] border-[rgba(63,73,89,0.3)] text-white focus:ring-0 focus:ring-offset-0 focus:border-[rgba(63,73,89,0.3)] focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-[rgba(63,73,89,0.3)]"
                value={newDumpText}
                onChange={(e) => setNewDumpText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    addBrainDumpItem();
                  }
                }}
              />
              <Button 
                onClick={addBrainDumpItem} 
                className="self-end rounded-full cursor-pointer"
                disabled={isAddingItem}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                {isAddingItem ? 'Adding...' : 'Add'}
              </Button>
            </div>
            
            <div className="mt-6 space-y-4">
              {isLoading ? (
                <div className="empty-column-state">
                  <p className="text-center text-gray-400 text-sm">
                    Loading your brain dumps...
                  </p>
                </div>
              ) : brainDumpItems.length === 0 ? (
                <div className="empty-column-state">
                  <p className="text-center text-gray-400 text-sm">
                    Your brain dump is empty. Add your thoughts above.
                  </p>
                </div>
              ) : (
                brainDumpItems.map((item) => (
                  <div 
                    key={item.id} 
                    className="border task-card border-gray-700 rounded-lg group relative p-4 hover:border-primary/50 transition-colors"
                  >
                    <p className="whitespace-pre-wrap break-words text-white">{item.content}</p>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-6 w-6 p-0 text-[#f87171] hover:text-[#ef4444] hover:bg-gray-700/50 cursor-pointer" 
                        onClick={() => removeBrainDumpItem(item.id)}
                      >
                        ×
                      </Button>
                    </div>
                    <div className="mt-2 text-xs text-gray-400">
                      {new Date(item.createdAt).toLocaleString()}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
} 