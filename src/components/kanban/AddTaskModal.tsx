'use client';

import { useState, useEffect, useCallback } from 'react';
import { useKanbanStore, Tag } from '@/lib/store';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { X, Calendar } from 'lucide-react';
import { format, parseISO } from 'date-fns';

// Define DayOfWeek locally or import from where it's defined (e.g., store or types file)
// For simplicity, defining locally if not shared elsewhere
type DayOfWeek = 'saturday' | 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday';
type TaskStatus = DayOfWeek | 'backlog';

interface AddTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedDate?: string;
}

export default function AddTaskModal({ isOpen, onClose, preselectedDate }: AddTaskModalProps) {
  const { addTask, goals, setGoals, tags, setTags } = useKanbanStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedGoalId, setSelectedGoalId] = useState<string | undefined>(undefined);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [newTag, setNewTag] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [date, setDate] = useState(preselectedDate || new Date().toISOString().split('T')[0]);
  const [isBacklog, setIsBacklog] = useState(false);

  const fetchGoals = useCallback(async () => {
    try {
      const response = await fetch('/api/goals');
      if (response.ok) {
        const data = await response.json();
        setGoals(data);
      } else {
        console.error('Failed to fetch goals');
      }
    } catch (error) {
      console.error('Error fetching goals:', error);
    }
  }, [setGoals]);

  const fetchTags = useCallback(async () => {
    try {
      const response = await fetch('/api/tags');
      if (response.ok) {
        const data = await response.json();
        setTags(data);
      } else {
        console.error('Failed to fetch tags');
      }
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  }, [setTags]);

  // Fetch goals and tags when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchGoals();
      fetchTags();
      
      // Reset date when preselectedDate changes
      if (preselectedDate) {
        setDate(preselectedDate);
      }
    }
  }, [isOpen, preselectedDate, fetchGoals, fetchTags]);



  const handleAddTag = () => {
    if (newTag.trim() === '') return;

    // Check if tag already exists in selected tags
    if (selectedTags.some((tag) => tag.name.toLowerCase() === newTag.toLowerCase())) {
      setNewTag('');
      return;
    }

    // Check if tag exists in all tags
    const existingTag = tags.find(
      (tag) => tag.name.toLowerCase() === newTag.toLowerCase()
    );

    if (existingTag) {
      setSelectedTags([...selectedTags, existingTag]);
    } else {
      // Create a temporary tag with a temporary ID
      const tempTag: Tag = {
        id: `temp-${Date.now()}`,
        name: newTag,
      };
      setSelectedTags([...selectedTags, tempTag]);
    }

    setNewTag('');
  };

  const handleRemoveTag = (tagToRemove: Tag) => {
    setSelectedTags(selectedTags.filter((tag) => tag.id !== tagToRemove.id));
  };

  const handleSubmit = async () => {
    if (!title.trim()) return;

    setIsLoading(true);

    try {
      let taskStatus: TaskStatus;
      let taskDate: string | undefined = date;
      
      // If backlog is selected, use 'backlog' status and no date
      if (isBacklog) {
        taskStatus = 'backlog';
        taskDate = undefined;
      } else {
        // Determine the day of the week from the selected date
        const selectedDate = parseISO(date);
        taskStatus = format(selectedDate, 'EEEE').toLowerCase() as DayOfWeek;
      }
      
      // Log data for debugging
      console.log('Submitting task with data:', {
        title,
        description,
        status: taskStatus,
        date: taskDate,
        goalId: selectedGoalId,
        tags: selectedTags.map(tag => ({ name: tag.name })),
        isBacklog
      });

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          status: taskStatus,
          date: taskDate,
          goalId: selectedGoalId,
          tags: selectedTags.map(tag => ({ name: tag.name })),
        }),
      });

      if (response.ok) {
        const newTask = await response.json();
        addTask(newTask);
        resetForm();
        onClose();
      } else {
        // Enhanced error logging
        const errorText = await response.text();
        console.error('Failed to create task. Server response:', response.status, errorText);
      }
    } catch (error) {
      console.error('Error creating task:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setSelectedGoalId(undefined);
    setSelectedTags([]);
    setNewTag('');
    // Don't reset date as it's dependent on preselectedDate
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title">Add New Task</DialogTitle>
        </DialogHeader>
        <div className="dialog-content space-y-4">
          <div className="form-field">
            <label htmlFor="title" className="form-label">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Task title"
              className="task-dialog-input"
            />
          </div>
          <div className="form-field">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Task description"
              className="task-dialog-input min-h-[100px]"
            />
          </div>
          <div className="form-field">
            <div className="flex items-center justify-between mb-2">
              <label htmlFor="backlog-toggle" className="form-label cursor-pointer flex items-center">
                Add to Backlog
                <span className="text-xs text-gray-400 ml-2">(Tasks without dates)</span>
              </label>
              <div className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  id="backlog-toggle" 
                  className="sr-only" 
                  checked={isBacklog}
                  onChange={() => setIsBacklog(!isBacklog)}
                />
                <div className={`w-11 h-6 rounded-full transition-colors ${isBacklog ? 'bg-amber-500' : 'bg-gray-700'}`}>
                  <div className={`absolute w-4 h-4 bg-white rounded-full transition-transform ${isBacklog ? 'translate-x-6' : 'translate-x-1'} top-1`}></div>
                </div>
              </div>
            </div>
            
            {!isBacklog && (
              <div className="form-field">
                <label htmlFor="date" className="form-label flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-primary/80" />
                  Date
                </label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  className="task-dialog-input h-10"
                  disabled={isBacklog}
                />
              </div>
            )}
          </div>
          <div className="form-field">
            <label htmlFor="goal" className="form-label">
              Goal
            </label>
            <select
              id="goal"
              value={selectedGoalId || ''}
              onChange={(e) => setSelectedGoalId(e.target.value || undefined)}
              className="flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a goal</option>
              {goals.map((goal) => (
                <option key={goal.id} value={goal.id}>
                  {goal.title}
                </option>
              ))}
            </select>
          </div>
          <div className="form-field">
            <label htmlFor="tags" className="form-label">
              Tags
            </label>
            <div className="flex gap-2">
              <Input
                id="tags"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                className="task-dialog-input"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <Button type="button" onClick={handleAddTag} size="sm" className="px-4">
                Add
              </Button>
            </div>
            {selectedTags.length > 0 && (
              <div className="flex flex-wrap gap-1.5 mt-3">
                {selectedTags.map((tag) => (
                  <Badge key={tag.id} variant="secondary" className="flex items-center gap-1 py-1 px-2">
                    {tag.name}
                    <X
                      className="h-3 w-3 cursor-pointer ml-1 opacity-70 hover:opacity-100"
                      onClick={() => handleRemoveTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
        <DialogFooter className="dialog-footer">
          <Button variant="outline" onClick={onClose} size="sm">
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading} size="sm">
            {isLoading ? 'Creating...' : 'Create Task'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
