'use client';

import { useDrop } from 'react-dnd';
import { Task } from '@/lib/store';
import TaskCard from './TaskCard';
import { useRef, useEffect } from 'react';

interface BacklogSheetProps {
  tasks: Task[];
  onMoveTask: (taskId: string, newStatus: 'backlog') => void;
}

export default function BacklogSheet({
  tasks,
  onMoveTask,
}: BacklogSheetProps) {
  const sheetRef = useRef<HTMLDivElement>(null);
  
  // Set up drop target
  const [{ isOver }, drop] = useDrop({
    accept: 'TASK',
    drop: (item: { id: string }) => {
      onMoveTask(item.id, 'backlog');
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });
  
  // Connect the drop ref
  useEffect(() => {
    if (sheetRef.current) {
      drop(sheetRef.current);
    }
  }, [drop]);

  return (
    <div
      ref={sheetRef}
      className={`backlog-sheet flex flex-col h-full border-r border-gray-800 ${
        isOver ? 'bg-gray-800/50' : ''
      }`}
      style={{ 
        width: '250px', 
        minWidth: '250px',
        display: 'flex', 
        flexDirection: 'column', 
        height: '100%' 
      }}
    >
      <div 
        className="backlog-header p-4 border-b border-gray-800"
        style={{ flexShrink: 0 }}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-amber-300">Backlog</h3>
            <span className="ml-2 text-gray-400">{tasks.length}</span>
          </div>
        </div>
      </div>

      <div 
        className="flex-1 overflow-y-auto p-2 min-h-0 scrollbar-hide"
        style={{ 
          flexGrow: 1, 
          overflowY: 'auto', 
          paddingBottom: '16px',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {tasks.length > 0 ? (
          <div 
            className="space-y-2"
            style={{ 
              paddingBottom: '24px',
              marginBottom: '16px'
            }}
          >
            {tasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </div>
        ) : (
          <div className="empty-column-state h-full flex items-center justify-center">
            <p className="text-gray-400 text-sm">No backlog tasks</p>
          </div>
        )}
      </div>
    </div>
  );
}
