'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { BrainDumpButton } from '@/components/brain-dump/BrainDump';
import { useAuth } from '@/lib/auth';
import { Button } from './button';
import { LogOut, User } from 'lucide-react';

export function Navbar() {
  const pathname = usePathname();
  const { user, signOut, isLoading } = useAuth();

  return (
    <nav className="fixed top-0 z-40 w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-shadow duration-300">
      <div className="container-fluid flex h-16 items-center justify-between">
        <div className="flex">
          <Link href="/" className="mr-6 flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <span className="font-bold text-xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70 dark:from-primary dark:to-primary/80">
              Kanbany
            </span>
          </Link>
          <div className="hidden md:flex items-center p-1 bg-gray-800/40 rounded-full hover:bg-gray-700/50 transition-colors">
            <Link
              href="/"
              className={cn(
                'text-sm font-medium px-4 py-1.5 rounded-full transition-all duration-200 hover:bg-indigo-600/20 hover:shadow-inner hover:scale-105',
                pathname === '/' || pathname === '/dashboard'
                  ? 'bg-indigo-600/30 text-amber-400 shadow-inner ring-1 ring-indigo-500/30'
                  : 'text-foreground/60 hover:text-amber-400'
              )}
            >
              Planning
            </Link>
            <Link
              href="/goals"
              className={cn(
                'text-sm font-medium px-4 py-1.5 rounded-full transition-all duration-200 hover:bg-emerald-600/20 hover:shadow-inner hover:scale-105',
                pathname === '/goals'
                  ? 'bg-emerald-600/30 text-teal-400 shadow-inner ring-1 ring-emerald-500/30'
                  : 'text-foreground/60 hover:text-teal-400'
              )}
            >
              Goals
            </Link>
            <Link
              href="/visions"
              className={cn(
                'text-sm font-medium px-4 py-1.5 rounded-full transition-all duration-200 hover:bg-purple-600/20 hover:shadow-inner hover:scale-105',
                pathname === '/visions'
                  ? 'bg-purple-600/30 text-violet-400 shadow-inner ring-1 ring-purple-500/30'
                  : 'text-foreground/60 hover:text-violet-400'
              )}
            >
              Visions
            </Link>
            <Link
              href="/purpose"
              className={cn(
                'text-sm font-medium px-4 py-1.5 rounded-full transition-all duration-200 hover:bg-pink-600/20 hover:shadow-inner hover:scale-105',
                pathname === '/purpose'
                  ? 'bg-pink-600/30 text-pink-400 shadow-inner ring-1 ring-pink-500/30'
                  : 'text-foreground/60 hover:text-pink-400'
              )}
            >
              Purpose
            </Link>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <BrainDumpButton />
          
          {user && (
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 bg-muted px-3 py-1 rounded-full">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">{user.email?.split('@')[0]}</span>
              </div>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={signOut}
                disabled={isLoading}
                title="Sign out"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}
