"use client"

import * as React from "react"
import { createContext, useContext, useEffect, useState } from "react"

type Theme = "dark" | "light" | "system"

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
  attribute?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
}

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "kanbany-ui-theme",
  attribute = "data-theme",
  enableSystem = true,
  disableTransitionOnChange = false,
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme)
  
  // Initialize theme from localStorage when component mounts (client side only)
  useEffect(() => {
    const storedTheme = typeof window !== 'undefined' 
      ? localStorage.getItem(storageKey) as Theme 
      : null
    
    if (storedTheme) {
      setTheme(storedTheme)
    }
  }, [storageKey])

  useEffect(() => {
    const root = window.document.documentElement

    // Remove old attribute value
    root.removeAttribute(attribute)

    // Add new attribute value - either class or data-theme
    if (attribute === "class") {
      if (theme === "dark") {
        root.classList.add("dark")
      } else {
        root.classList.remove("dark")
      }
    } else {
      if (theme !== "system") {
        root.setAttribute(attribute, theme)
      }
    }

    if (enableSystem && theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark"
        : "light"

      if (attribute === "class") {
        if (systemTheme === "dark") {
          root.classList.add("dark")
        } else {
          root.classList.remove("dark")
        }
      } else {
        root.setAttribute(attribute, systemTheme)
      }
    }

    // Handle transitions
    if (disableTransitionOnChange) {
      root.classList.add("[&_*]:!transition-none")
      window.setTimeout(() => {
        root.classList.remove("[&_*]:!transition-none")
      }, 0)
    }
  }, [theme, attribute, enableSystem, disableTransitionOnChange])

  // Update localStorage when theme changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, theme)
    }
  }, [theme, storageKey])

  // Listen for system theme changes
  useEffect(() => {
    if (!enableSystem) return

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    
    const onSystemThemeChange = () => {
      if (theme === "system") {
        const systemTheme = mediaQuery.matches ? "dark" : "light"
        const root = window.document.documentElement
        
        if (attribute === "class") {
          if (systemTheme === "dark") {
            root.classList.add("dark")
          } else {
            root.classList.remove("dark")
          }
        } else {
          root.setAttribute(attribute, systemTheme)
        }
      }
    }

    mediaQuery.addEventListener("change", onSystemThemeChange)
    
    return () => {
      mediaQuery.removeEventListener("change", onSystemThemeChange)
    }
  }, [theme, attribute, enableSystem])

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      setTheme(theme)
    },
  }

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)
  
  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")
    
  return context
} 