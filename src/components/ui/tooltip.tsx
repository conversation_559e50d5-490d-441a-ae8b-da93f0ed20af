"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface TooltipProps {
  content: React.ReactNode
  children: React.ReactNode
  className?: string
  side?: "top" | "right" | "bottom" | "left"
}

export function Tooltip({
  content,
  children,
  className,
  side = "top",
  ...props
}: TooltipProps) {
  const [isVisible, setIsVisible] = React.useState(false)
  const tooltipRef = React.useRef<HTMLDivElement>(null)

  // Handle click outside to close popover
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
        setIsVisible(false)
      }
    }

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isVisible])

  // Calculate position classes based on side prop
  const positionClasses = {
    top: "bottom-full left-1/2 transform -translate-x-1/2 -translate-y-1 mb-1",
    right: "left-full top-1/2 transform -translate-y-1/2 translate-x-1 ml-1",
    bottom: "top-full left-1/2 transform -translate-x-1/2 translate-y-1 mt-1",
    left: "right-full top-1/2 transform -translate-y-1/2 -translate-x-1 mr-1"
  }

  const toggleTooltip = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsVisible(!isVisible)
  }

  return (
    <div 
      ref={tooltipRef}
      className="relative inline-block" 
      onClick={toggleTooltip}
      {...props}
    >
      {children}
      {isVisible && (
        <div
          className={cn(
            "absolute z-[100] px-3 py-2 text-sm rounded-md bg-gray-800 text-white shadow-md",
            positionClasses[side],
            className
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {content}
        </div>
      )}
    </div>
  )
} 