import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';

// Validate environment variables
function validateEnvVars() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!url || !key) {
    throw new Error('Missing required Supabase environment variables');
  }
  
  return { url, key };
}

// Create a Supabase server client
export function getServerSupabase() {
  const { url, key } = validateEnvVars();
  
  // In Next.js 15, cookies() returns ReadonlyRequestCookies directly, not a Promise
  // We need to access it in a way that TypeScript understands
  const cookieStore = cookies();
  
  // Create a client with the cookie handler
  return createServerClient(url, key, {
    cookies: {
      get(name: string) {
        // Access the cookie from the store
        // @ts-expect-error - Next.js 15 cookies() typing issue with Supabase
        return cookieStore.get(name)?.value;
      },
      set: () => {
        // Server components can't set cookies
      },
      remove: () => {
        // Server components can't remove cookies
      },
    },
  });
}

// Get the current user's session
export async function getServerSession() {
  try {
    const supabase = getServerSupabase(); // Remove await here
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session:', error);
      return null;
    }
    
    return session;
  } catch (error) {
    console.error('Error in getServerSession:', error);
    return null;
  }
}

// Check if a user is authenticated on the server
export async function requireAuth() {
  try {
    const session = await getServerSession();
    
    if (!session?.user) {
      return null;
    }
    
    return session.user;
  } catch (error) {
    console.error('Error in requireAuth:', error);
    return null;
  }
}

// Get current user with error handling
export async function getCurrentUser() {
  try {
    const supabase = getServerSupabase();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('Error getting user:', error);
      return null;
    }
    
    return user;
  } catch (error) {
    console.error('Error in getCurrentUser:', error);
    return null;
  }
} 