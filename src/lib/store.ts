import { create } from 'zustand';

// Define types for our store
export type Task = {
  id: string;
  title: string;
  description?: string;
  status: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday' | 'backlog';
  date?: string; // ISO format date string (YYYY-MM-DD)
  goalId?: string;
  tags: Tag[];
  goal?: Goal;
  completed?: boolean; // Added for completion tracking
  userId?: string; // User ID for task ownership
};

export type Tag = {
  id: string;
  name: string;
};

export type Goal = {
  id: string;
  title: string;
  description?: string;
  visionId?: string;
  vision?: Vision;
  tasks?: Task[];
};

export type Vision = {
  id: string;
  title: string;
  description?: string;
  goals?: Goal[];
};

export type BrainDump = {
  id: string;
  content: string;
  createdAt: string;
  userId: string;
};

export type Purpose = {
  id: string;
  mission: string;
  userId?: string;
};

// Define the store
type KanbanStore = {
  tasks: Task[];
  goals: Goal[];
  visions: Vision[];
  tags: Tag[];
  purpose: Purpose | null;

  // Task actions
  setTasks: (tasks: Task[]) => void;
  addTask: (task: Task) => void;
  updateTask: (id: string, task: Partial<Task>) => void;
  deleteTask: (id: string) => void;
  moveTask: (id: string, status: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday' | 'backlog') => void;

  // Goal actions
  setGoals: (goals: Goal[]) => void;
  addGoal: (goal: Goal) => void;
  updateGoal: (id: string, goal: Partial<Goal>) => void;
  deleteGoal: (id: string) => void;

  // Vision actions
  setVisions: (visions: Vision[]) => void;
  addVision: (vision: Vision) => void;
  updateVision: (id: string, vision: Partial<Vision>) => void;
  deleteVision: (id: string) => void;

  // Tag actions
  setTags: (tags: Tag[]) => void;
  addTag: (tag: Tag) => void;
  updateTag: (id: string, tag: Partial<Tag>) => void;
  deleteTag: (id: string) => void;
  
  // Purpose actions
  setPurpose: (purpose: Purpose | null) => void;
  updatePurpose: (purpose: Partial<Purpose>) => void;
};

export const useKanbanStore = create<KanbanStore>((set) => ({
  tasks: [],
  goals: [],
  visions: [],
  tags: [],
  purpose: null,

  // Task actions
  setTasks: (tasks) => set({ tasks }),
  addTask: (task) => set((state) => ({ tasks: [...state.tasks, task] })),
  updateTask: (id, updatedTask) => set((state) => ({
    tasks: state.tasks.map((task) => (task.id === id ? { ...task, ...updatedTask } : task)),
  })),
  deleteTask: (id) => set((state) => ({
    tasks: state.tasks.filter((task) => task.id !== id),
  })),
  moveTask: (id, status) => set((state) => ({
    tasks: state.tasks.map((task) => (task.id === id ? { ...task, status } : task)),
  })),

  // Goal actions
  setGoals: (goals) => set({ goals }),
  addGoal: (goal) => set((state) => ({ goals: [...state.goals, goal] })),
  updateGoal: (id, updatedGoal) => set((state) => ({
    goals: state.goals.map((goal) => (goal.id === id ? { ...goal, ...updatedGoal } : goal)),
  })),
  deleteGoal: (id) => set((state) => ({
    goals: state.goals.filter((goal) => goal.id !== id),
  })),

  // Vision actions
  setVisions: (visions) => set({ visions }),
  addVision: (vision) => set((state) => ({ visions: [...state.visions, vision] })),
  updateVision: (id, updatedVision) => set((state) => ({
    visions: state.visions.map((vision) => (vision.id === id ? { ...vision, ...updatedVision } : vision)),
  })),
  deleteVision: (id) => set((state) => ({
    visions: state.visions.filter((vision) => vision.id !== id),
  })),

  // Tag actions
  setTags: (tags) => set({ tags }),
  addTag: (tag) => set((state) => ({ tags: [...state.tags, tag] })),
  updateTag: (id, updatedTag) => set((state) => ({
    tags: state.tags.map((tag) => (tag.id === id ? { ...tag, ...updatedTag } : tag)),
  })),
  deleteTag: (id) => set((state) => ({
    tags: state.tags.filter((tag) => tag.id !== id),
  })),
  
  // Purpose actions
  setPurpose: (purpose) => set({ purpose }),
  updatePurpose: (purpose) => set((state) => ({
    purpose: state.purpose ? { ...state.purpose, ...purpose } : purpose as Purpose,
  })),
}));
