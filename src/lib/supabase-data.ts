import { supabase } from './supabase';
import { createClient_browser } from './supabase';
import { Task, Goal, Vision, Tag, Purpose } from './store';
import { randomUUID } from 'crypto';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// Database response interfaces
interface DbVision {
  id: string;
  title: string;
  description?: string;
  userId?: string;
  goals?: DbGoal[];
}

interface DbGoal {
  id: string;
  title: string;
  description?: string;
  visionId?: string;
  userId?: string;
  vision?: DbVision;
  tasks?: DbTask[];
}

interface DbTask {
  id: string;
  title: string;
  description?: string;
  status: string;
  date?: string;
  goalId?: string;
  userId?: string;
  completed?: boolean;
  tags?: DbTagRelation[];
}

interface DbTagRelation {
  tag: DbTag;
}

interface DbTag {
  id: string;
  name: string;
}

// Helper to get the current user ID - works in both client and server contexts
export const getCurrentUserId = async () => {
  try {
    // Try server-side first (for API routes)
    if (typeof window === 'undefined') {
      try {
        const cookieStore = await cookies();
        const serverClient = createServerClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          {
            cookies: {
              get(name) {
                return cookieStore.get(name)?.value;
              },
              set() {},
              remove() {},
            },
          }
        );
        
        const { data: { user } } = await serverClient.auth.getUser();
        return user?.id || null;
      } catch (serverError) {
        console.error('Error getting user server-side:', serverError);
        // Fall back to client-side if server-side fails
      }
    }
    
    // Client-side approach
    const client = createClient_browser();
    const { data: { user } } = await client.auth.getUser();
    return user?.id || null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// VISION OPERATIONS
export const fetchVisions = async () => {
  const userId = await getCurrentUserId();
  
  // Query without filter if userId is null (fetches all visions)
  const query = supabase
    .from('Vision')
    .select(`
      *,
      goals:Goal(
        *,
        tasks:Task(
          *,
          tags:_TagToTask(
            tag:Tag(*)
          )
        )
      )
    `);
  
  // Only filter by userId if it's not null
  const { data, error } = userId 
    ? await query.eq('userId', userId)
    : await query;

  if (error) {
    console.error('Error fetching visions:', error);
    throw error;
  }

  // Transform the data to match the expected format
  const transformedData = data.map((vision: DbVision) => {
    const goals = vision.goals?.map((goal: DbGoal) => {
      const tasks = goal.tasks?.map((task: DbTask) => {
        const tags = task.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
        return {
          ...task,
          tags
        } as Task;
      }) || [];
      
      return {
        ...goal,
        tasks
      } as Goal;
    }) || [];
    
    return {
      ...vision,
      goals
    } as Vision;
  });

  return transformedData;
};

export const createVision = async (vision: Partial<Vision>) => {
  const userId = await getCurrentUserId();
  
  // Generate a UUID for the vision ID
  const visionId = randomUUID();
  
  const { data, error } = await supabase
    .from('Vision')
    .insert({ id: visionId, ...vision, userId })
    .select()
    .single();

  if (error) {
    console.error('Error creating vision:', error);
    throw error;
  }

  return data;
};

export const updateVision = async (id: string, vision: Partial<Vision>) => {
  const { data, error } = await supabase
    .from('Vision')
    .update(vision)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating vision:', error);
    throw error;
  }

  return data;
};

export const deleteVision = async (id: string) => {
  const { error } = await supabase
    .from('Vision')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting vision:', error);
    throw error;
  }

  return true;
};

// GOAL OPERATIONS
export const fetchGoals = async () => {
  const userId = await getCurrentUserId();
  
  // Query without filter if userId is null (fetches all goals)
  const query = supabase
    .from('Goal')
    .select(`
      *,
      vision:Vision(*),
      tasks:Task(
        *,
        tags:_TagToTask(
          tag:Tag(*)
        )
      )
    `);
    
  // Only filter by userId if it's not null
  const { data, error } = userId 
    ? await query.eq('userId', userId)
    : await query;

  if (error) {
    console.error('Error fetching goals:', error);
    throw error;
  }

  // Transform the data to match the expected format
  const transformedData = data.map((goal: DbGoal) => {
    const tasks = goal.tasks?.map((task: DbTask) => {
      const tags = task.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
      return {
        ...task,
        tags
      } as Task;
    }) || [];
    
    return {
      ...goal,
      tasks
    } as Goal;
  });

  return transformedData;
};

export const createGoal = async (goal: Partial<Goal>) => {
  const userId = await getCurrentUserId();
  
  // Generate a UUID for the goal ID
  const goalId = randomUUID();
  
  const { data, error } = await supabase
    .from('Goal')
    .insert({ id: goalId, ...goal, userId })
    .select()
    .single();

  if (error) {
    console.error('Error creating goal:', error);
    throw error;
  }

  return data;
};

export const updateGoal = async (id: string, goal: Partial<Goal>) => {
  const { data, error } = await supabase
    .from('Goal')
    .update(goal)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating goal:', error);
    throw error;
  }

  return data;
};

export const deleteGoal = async (id: string) => {
  const { error } = await supabase
    .from('Goal')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting goal:', error);
    throw error;
  }

  return true;
};

// TASK OPERATIONS
export const fetchTasks = async () => {
  const userId = await getCurrentUserId();
  
  // Query without filter if userId is null (fetches all tasks)
  const query = supabase
    .from('Task')
    .select(`
      *,
      goal:Goal(*),
      tags:_TagToTask(
        tag:Tag(*)
      )
    `);
  
  // Only filter by userId if it's not null
  const { data, error } = userId 
    ? await query.eq('userId', userId)
    : await query;

  if (error) {
    console.error('Error fetching tasks:', error);
    throw error;
  }

  // Transform the data to match the expected format
  const transformedData = data.map((task: DbTask) => {
    const tags = task.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
    return {
      ...task,
      tags
    } as Task;
  });

  return transformedData;
};

export const createTask = async (task: Partial<Task> & { tags?: Partial<Tag>[] }) => {
  const userId = await getCurrentUserId();
  
  // Generate a UUID for the task ID since Supabase expects us to provide it
  const taskId = randomUUID();
  
  // First, create the task without tags
  const { data: newTask, error: taskError } = await supabase
    .from('Task')
    .insert({ 
      id: taskId,
      title: task.title,
      description: task.description,
      status: task.status,
      date: task.date,
      goalId: task.goalId,
      completed: task.completed || false,
      userId 
    })
    .select()
    .single();

  if (taskError) {
    console.error('Error creating task:', taskError);
    throw taskError;
  }

  // Then handle tags if they exist
  if (task.tags && task.tags.length > 0) {
    // Process tags sequentially to avoid race conditions
    for (const tag of task.tags) {
      // Check if tag exists
      const { data: existingTags, error: findTagError } = await supabase
        .from('Tag')
        .select('*')
        .eq('name', tag.name)
        .limit(1);

      if (findTagError) {
        console.error('Error finding tag:', findTagError);
        continue;
      }

      let tagId;
      
      if (existingTags && existingTags.length > 0) {
        // Use existing tag
        tagId = existingTags[0].id;
      } else {
        // Create new tag with generated ID
        const newTagId = randomUUID();
        const { data: newTag, error: createTagError } = await supabase
          .from('Tag')
          .insert({ id: newTagId, name: tag.name })
          .select()
          .single();

        if (createTagError) {
          console.error('Error creating tag:', createTagError);
          continue;
        }

        tagId = newTag.id;
      }

      // Create relation between task and tag
      const { error: relationError } = await supabase
        .from('_TagToTask')
        .insert({ 
          A: tagId, // Tag ID
          B: newTask.id // Task ID
        });

      if (relationError) {
        console.error('Error creating tag relationship:', relationError);
      }
    }
  }

  // Fetch the task with tags to return
  const { data: taskWithTags, error: fetchError } = await supabase
    .from('Task')
    .select(`
      *,
      goal:Goal(*),
      tags:_TagToTask(
        tag:Tag(*)
      )
    `)
    .eq('id', newTask.id)
    .single();

  if (fetchError) {
    console.error('Error fetching task with tags:', fetchError);
    return newTask; // Return task without tags if error
  }

  // Transform the data to match the expected format
  const tags = taskWithTags.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
  return {
    ...taskWithTags,
    tags
  };
};

export const updateTask = async (id: string, task: Partial<Task> & { tags?: Partial<Tag>[] }) => {
  // Update task properties excluding tags
  const { title, description, status, date, goalId, completed } = task;
  const updateData: Record<string, unknown> = {};
  
  if (title !== undefined) updateData.title = title;
  if (description !== undefined) updateData.description = description;
  if (status !== undefined) updateData.status = status;
  if (date !== undefined) updateData.date = date;
  if (goalId !== undefined) updateData.goalId = goalId;
  if (completed !== undefined) updateData.completed = completed;
  
  const { data: updatedTask, error: updateError } = await supabase
    .from('Task')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (updateError) {
    console.error('Error updating task:', updateError);
    throw updateError;
  }

  // Handle tags if they were provided
  if (task.tags !== undefined) {
    // First, remove all existing tag relationships
    const { error: deleteError } = await supabase
      .from('_TagToTask')
      .delete()
      .eq('B', id); // B is the task ID in the relation table

    if (deleteError) {
      console.error('Error removing existing tag relationships:', deleteError);
    }

    // Add new tag relationships
    if (task.tags && task.tags.length > 0) {
      for (const tag of task.tags) {
        // Check if tag exists
        const { data: existingTags, error: findTagError } = await supabase
          .from('Tag')
          .select('*')
          .eq('name', tag.name)
          .limit(1);

        if (findTagError) {
          console.error('Error finding tag:', findTagError);
          continue;
        }

        let tagId;
        
        if (existingTags && existingTags.length > 0) {
          // Use existing tag
          tagId = existingTags[0].id;
        } else {
          // Create new tag with UUID
          const newTagId = randomUUID();
          const { data: newTag, error: createTagError } = await supabase
            .from('Tag')
            .insert({ id: newTagId, name: tag.name })
            .select()
            .single();

          if (createTagError) {
            console.error('Error creating tag:', createTagError);
            continue;
          }

          tagId = newTag.id;
        }

        // Create relation between task and tag
        const { error: relationError } = await supabase
          .from('_TagToTask')
          .insert({ 
            A: tagId, // Tag ID
            B: id // Task ID
          });

        if (relationError) {
          console.error('Error creating tag relationship:', relationError);
        }
      }
    }
  }

  // Fetch the task with tags to return
  const { data: taskWithTags, error: fetchError } = await supabase
    .from('Task')
    .select(`
      *,
      goal:Goal(*),
      tags:_TagToTask(
        tag:Tag(*)
      )
    `)
    .eq('id', id)
    .single();

  if (fetchError) {
    console.error('Error fetching task with tags:', fetchError);
    return updatedTask; // Return task without tags if error
  }

  // Transform the data to match the expected format
  const tags = taskWithTags.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
  return {
    ...taskWithTags,
    tags
  };
};

export const deleteTask = async (id: string) => {
  // First, delete all tag relationships
  const { error: relationError } = await supabase
    .from('_TagToTask')
    .delete()
    .eq('B', id); // B is the task ID

  if (relationError) {
    console.error('Error deleting tag relationships:', relationError);
  }

  // Then delete the task
  const { error } = await supabase
    .from('Task')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting task:', error);
    throw error;
  }

  return true;
};

// BRAIN DUMP OPERATIONS  
export const fetchBrainDumps = async () => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  const { data, error } = await supabase
    .from('BrainDump')
    .select('*')
    .eq('userId', userId)
    .order('createdAt', { ascending: false });

  if (error) {
    console.error('Error fetching brain dumps:', error);
    throw error;
  }

  return data;
};

export const createBrainDump = async (brainDump: { content: string }) => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  // Generate a UUID for the brain dump ID
  const brainDumpId = randomUUID();
  
  const { data, error } = await supabase
    .from('BrainDump')
    .insert({ 
      id: brainDumpId, 
      content: brainDump.content, 
      userId 
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating brain dump:', error);
    throw error;
  }

  return data;
};

export const deleteBrainDump = async (id: string) => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  // Ensure user can only delete their own brain dumps
  const { error } = await supabase
    .from('BrainDump')
    .delete()
    .eq('id', id)
    .eq('userId', userId);

  if (error) {
    console.error('Error deleting brain dump:', error);
    throw error;
  }

  return true;
}; 

// PURPOSE OPERATIONS
export const fetchPurpose = async () => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  const { data, error } = await supabase
    .from('Purpose')
    .select('*')
    .eq('userId', userId)
    .maybeSingle();

  if (error) {
    console.error('Error fetching purpose:', error);
    throw error;
  }

  return data;
};

export const createPurpose = async (purpose: Partial<Purpose>) => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  // Generate a UUID for the purpose ID
  const purposeId = randomUUID();
  
  const { data, error } = await supabase
    .from('Purpose')
    .insert({ id: purposeId, ...purpose, userId })
    .select()
    .single();

  if (error) {
    console.error('Error creating purpose:', error);
    throw error;
  }

  return data;
};

export const updatePurpose = async (id: string, purpose: Partial<Purpose>) => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  const { data, error } = await supabase
    .from('Purpose')
    .update(purpose)
    .eq('id', id)
    .eq('userId', userId)
    .select()
    .single();

  if (error) {
    console.error('Error updating purpose:', error);
    throw error;
  }

  return data;
};

export const deletePurpose = async (id: string) => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    throw new Error('User not authenticated');
  }
  
  const { error } = await supabase
    .from('Purpose')
    .delete()
    .eq('id', id)
    .eq('userId', userId);

  if (error) {
    console.error('Error deleting purpose:', error);
    throw error;
  }

  return true;
};