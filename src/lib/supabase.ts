import { createBrowserClient } from '@supabase/ssr';
import { createClient } from '@supabase/supabase-js';

// Safely access environment variables
const getSupabaseUrl = () => {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!url) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
  }
  return url;
};

const getSupabaseAnonKey = () => {
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!key) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required');
  }
  return key;
};

// Create a Supabase client for the browser
export const createClient_browser = () => {
  return createBrowserClient(
    getSupabaseUrl(),
    getSupabaseAnonKey()
  );
};

// Create a Supabase client for server components and API routes
export const supabase = createClient(
  getSupabaseUrl(),
  getSupabaseA<PERSON><PERSON>ey()
); 