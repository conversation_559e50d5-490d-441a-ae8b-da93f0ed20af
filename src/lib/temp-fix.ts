// Fixed createTask function snippet
export const createTaskFix = `
  // Transform the data to match the expected format
  const tags = taskWithTags.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
  return {
    ...taskWithTags,
    tags
  } as Task;
`;

// Fixed updateTask function snippet
export const updateTaskFix = `
  // Transform the data to match the expected format
  const tags = taskWithTags.tags?.map((tagRel: DbTagRelation) => tagRel.tag) || [];
  return {
    ...taskWithTags,
    tags
  } as Task;
`;
