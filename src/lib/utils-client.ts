'use client';

import { createClient_browser } from './supabase';
import { Task, Goal, Vision } from './store';

// Helper to make authenticated fetch requests
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {}
) => {
  const supabase = createClient_browser();
  const { data } = await supabase.auth.getSession();
  
  if (!data.session) {
    throw new Error('No authenticated session found');
  }
  
  const headers = new Headers(options.headers || {});
  headers.set('Authorization', `Bearer ${data.session.access_token}`);
  
  return fetch(url, {
    ...options,
    headers,
  });
};

// Custom React hooks for data operations (to be used within React components)
export function useTaskOperations() {
  const supabase = createClient_browser();
  
  // Get current user ID
  const getUserId = async () => {
    const { data } = await supabase.auth.getSession();
    return data.session?.user.id;
  };
  
  // Create a task with authenticated user
  const createTask = async (taskData: Partial<Task>) => {
    const userId = await getUserId();
    
    if (!userId) {
      throw new Error('User not authenticated');
    }
    
    return fetch('/api/tasks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...taskData,
        userId,
      }),
    });
  };
  
  return { createTask };
}

export function useGoalOperations() {
  const supabase = createClient_browser();
  
  // Get current user ID
  const getUserId = async () => {
    const { data } = await supabase.auth.getSession();
    return data.session?.user.id;
  };
  
  // Create a goal with authenticated user
  const createGoal = async (goalData: Partial<Goal>) => {
    const userId = await getUserId();
    
    if (!userId) {
      throw new Error('User not authenticated');
    }
    
    return fetch('/api/goals', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...goalData,
        userId,
      }),
    });
  };
  
  return { createGoal };
}

export function useVisionOperations() {
  const supabase = createClient_browser();
  
  // Get current user ID
  const getUserId = async () => {
    const { data } = await supabase.auth.getSession();
    return data.session?.user.id;
  };
  
  // Create a vision with authenticated user
  const createVision = async (visionData: Partial<Vision>) => {
    const userId = await getUserId();
    
    if (!userId) {
      throw new Error('User not authenticated');
    }
    
    return fetch('/api/visions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...visionData,
        userId,
      }),
    });
  };
  
  return { createVision };
} 