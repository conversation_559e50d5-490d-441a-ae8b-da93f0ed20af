import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Create a response to modify
  const response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Create a Supabase client
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return request.cookies.get(name)?.value;
        },
        set(name, value, options: CookieOptions) {
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name, options) {
          response.cookies.set({
            name,
            value: '',
            ...options,
            maxAge: 0,
          });
        },
      },
    }
  );

  // Refresh session if expired - required for server components
  const { data: { session } } = await supabase.auth.getSession();

  // Auth logic based on route
  const isAuthRoute = pathname.startsWith('/auth');
  const isApiRoute = pathname.startsWith('/api');
  const isRootPath = pathname === '/';
  const isLandingPage = pathname === '/landing';
  const isPublicRoute = isRootPath || isLandingPage || isAuthRoute || isApiRoute;
  
  // If the user is not signed in and the route is not a public route, redirect to landing
  if (!session && !isPublicRoute) {
    return NextResponse.redirect(new URL('/landing', request.url));
  }

  // If the user is signed in and the route is an auth route, redirect to home
  if (session && isAuthRoute && pathname !== '/auth/callback') {
    return NextResponse.redirect(new URL('/', request.url));
  }

  return response;
}

// Configure the paths where the middleware should run
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}; 