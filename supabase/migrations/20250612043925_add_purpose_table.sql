-- Create Purpose table
CREATE TABLE IF NOT EXISTS "Purpose" (
  "id" TEXT PRIMARY KEY,
  "mission" TEXT NOT NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "userId" UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- <PERSON>reate index
CREATE INDEX IF NOT EXISTS "Purpose_userId_idx" ON "Purpose"("userId");

-- Create trigger to update the updatedAt field
CREATE OR REPLACE TRIGGER set_timestamp_purpose
BEFORE UPDATE ON "Purpose"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
